import Draggable from "react-draggable";
import { useEffect, useState, useCallback, type FC } from "react";
import { Application } from "../types";
import { parseJobDetails } from "../utils/jobParser";
import { createRoot } from "react-dom/client";
import { Marked } from "@ts-stack/markdown";
import { getHotkey } from "../utils/storage";
// Removed UI component imports - using custom CSS instead

const JobTrackerDialog: FC = () => {
    const [jobDetails, setJobDetails] = useState<Application>();
    const [isLoading, setIsLoading] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    const [isFailed, setIsFailed] = useState(false);

    const fetchJobDetail = useCallback(async () => {
        try {
            setIsLoading(true);
            const detail = await parseJobDetails();
            console.log('success', detail);
            if ('success' in detail && detail.success === false) {
                setIsFailed(true);
                showToast(detail.message || "Failed to fetch job details", 'error');
                return;
            } if ('companyName' in detail) setJobDetails(detail);
        } catch (error) {
            console.error("Error parsing job details:", error);
            showToast("Failed to fetch job details", 'error');
            setIsFailed(true);
        } finally {
            setIsLoading(false);
        }
    }, []);
    useEffect(() => {
        fetchJobDetail();
    }, [fetchJobDetail]);



    // Use the global showToast function instead of React state
    const showToast = (message: string, type: 'info' | 'success' | 'error' | 'loading' = 'info') => {
        // Call the global showToast function defined at the bottom of the file
        (window as any).showJobTrackerToast(message, type);
    };

    const handleSave = async () => {
        if (!jobDetails) return;

        try {
            setIsSaving(true);
            chrome.runtime.sendMessage({
                type: 'SAVE_JOB',
                payload: jobDetails
            }, (response) => {
                console.log(response);
                if (response.success) {
                    showToast('Job application saved successfully!', 'success');
                    setTimeout(() => {
                        handleClose();
                    }, 1500);
                } else {
                    showToast(response.message || 'Failed to save job application', 'error');
                }
            })
        } catch (error) {
            console.error("Error saving job:", error);
            showToast("Failed to save job. Please try again.", 'error');
        } finally {
            setIsSaving(false);
        }
    };

    useEffect(() => {
        const messageListener = (message: any, _sender: any, sendResponse: any) => {
            console.log('Content script received message:', message);
            if (message.type === 'SHOW_DIALOG') {
                console.log('Showing dialog...');
                injectDialog();
                sendResponse({ success: true });
            }
        };

        chrome.runtime.onMessage.addListener(messageListener);

        // Cleanup function to remove listener
        return () => {
            chrome.runtime.onMessage.removeListener(messageListener);
        };
    }, []);

    const handleClose = () => {
        const dialog = document.getElementById('job-tracker-dialog-container');
        if (dialog) {
            dialog.remove();
        }
    };

    return (
        <Draggable
                handle=".job-tracker-handle"
                cancel={'input, textarea, button, select'}
            >
                <div className="job-tracker-dialog">
                    <div className="job-tracker-dialog-header">
                        <div className="job-tracker-handle">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                            </svg>
                            Job Application Tracker
                        </div>
                        <button
                            onClick={handleClose}
                            className="job-tracker-close"
                            aria-label="Close dialog"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                <div className="job-tracker-dialog-content">

                    {isLoading ? (
                        <div className="loading-state" style={{
                            padding: '40px 20px',
                            textAlign: 'center',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            minHeight: '200px',
                            background: 'transparent'
                        }}>
                            <div className="loading-content" style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '16px',
                                background: 'transparent'
                            }}>
                                <svg
                                    style={{
                                        width: '40px',
                                        height: '40px',
                                        animation: 'spin 1s linear infinite'
                                    }}
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        style={{ opacity: 0.25 }}
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="#3b82f6"
                                        strokeWidth="4"
                                    />
                                    <path
                                        style={{ opacity: 0.75 }}
                                        fill="#3b82f6"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                                    />
                                </svg>
                                <h3 style={{
                                    fontSize: '18px',
                                    fontWeight: '600',
                                    color: '#111827',
                                    margin: '0',
                                    padding: '0',
                                    textAlign: 'center',
                                    lineHeight: '1.4',
                                    background: 'transparent',
                                    border: 'none'
                                }}>Extracting Job Details</h3>
                                <p style={{
                                    color: '#6b7280',
                                    fontSize: '14px',
                                    margin: '0',
                                    padding: '0',
                                    textAlign: 'center',
                                    lineHeight: '1.4',
                                    background: 'transparent',
                                    border: 'none'
                                }}>Please wait while we analyze the job posting...</p>
                            </div>
                        </div>
                    ) : jobDetails ? (
                        <div>
                            <div className="form-field">
                                <label className="input-label">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ display: 'inline', marginRight: '8px' }}>
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m-8 0V6a2 2 0 00-2 2v6" />
                                    </svg>
                                    Job Title
                                </label>
                                <input
                                    className="input-field"
                                    type="text"
                                    value={jobDetails.jobRole}
                                    onChange={(e) => setJobDetails({ ...jobDetails, jobRole: e.target.value })}
                                    placeholder="Enter job title"
                                />
                            </div>

                            <div className="form-field">
                                <label className="input-label">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ display: 'inline', marginRight: '8px' }}>
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    Company
                                </label>
                                <input
                                    className="input-field"
                                    type="text"
                                    value={jobDetails.companyName}
                                    onChange={(e) => setJobDetails({ ...jobDetails, companyName: e.target.value })}
                                    placeholder="Enter company name"
                                />
                            </div>
                                {/* Company Details */}
                                {(jobDetails?.companyNationality || jobDetails?.companySite) && (
                                    <div className="info-grid">
                                        {jobDetails?.companyNationality && (
                                            <div className="info-card">
                                                <div className="info-card-label">Company Nationality</div>
                                                <div className="info-card-value">{jobDetails.companyNationality}</div>
                                            </div>
                                        )}
                                        {jobDetails?.companySite && (
                                            <div className="info-card">
                                                <div className="info-card-label">Company Site</div>
                                                <div className="info-card-value">{jobDetails.companySite}</div>
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Remote Availability */}
                                {jobDetails.remoteAvailability && (
                                    <div className="remote-highlight">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <div>
                                            <div className="remote-label">Remote Availability</div>
                                            <div className="remote-value">{jobDetails.remoteAvailability}</div>
                                        </div>
                                    </div>
                                )}

                                {/* Tech Stack */}
                                <div className="content-section">
                                    <div className="section-label">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                        </svg>
                                        Tech Stack
                                    </div>
                                    {jobDetails.techStacks.length > 0 ? (
                                        <div className="tech-stack-container">
                                            {jobDetails.techStacks.map((tech, index) => (
                                                <span key={index} className="tech-chip">
                                                    {tech}
                                                </span>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="no-content">
                                            <p>No tech stack detected</p>
                                        </div>
                                    )}
                                </div>

                                {/* Job Description */}
                                <div className="content-section">
                                    <div className="section-label">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        Job Description
                                    </div>
                                    <div className="job-description">
                                        <div
                                            className="prose"
                                            dangerouslySetInnerHTML={{
                                                __html: Marked.parse(jobDetails.jobDescription || "No description available")
                                            }}
                                        />
                                    </div>
                                </div>

                            </div>
                        ) : (
                            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                                <div style={{
                                    background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
                                    padding: '16px',
                                    borderRadius: '50%',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginBottom: '16px',
                                    border: '1px solid #fecaca'
                                }}>
                                    <svg style={{ width: '32px', height: '32px', color: '#dc2626' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', marginBottom: '8px' }}>Failed to Extract Job Details</h3>
                                <p style={{ color: '#6b7280', marginBottom: '24px' }}>We couldn't automatically extract the job information from this page.</p>
                                <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
                                    {isFailed && (
                                        <button
                                            onClick={fetchJobDetail}
                                            className="btn btn-outline"
                                        >
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                            Retry
                                        </button>
                                    )}
                                    <button
                                        onClick={handleClose}
                                        className="btn btn-primary"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Save Button - sticky at bottom, always visible when job details exist */}
                    {jobDetails && (
                        <div className="save-button-container">
                            <button
                                onClick={handleSave}
                                disabled={isSaving}
                                className="save-button"
                            >
                                {isSaving ? (
                                    <>
                                        <div className="job-tracker-toast-spinner" style={{ width: '16px', height: '16px', borderWidth: '2px' }}></div>
                                        Saving Application...
                                    </>
                                ) : (
                                    <>
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                        </svg>
                                        Save Job Application
                                    </>
                                )}
                            </button>
                        </div>
                    )}
                </div>
            </Draggable>
    )
};

const injectDialog = async () => {
    const existingDialog = document.getElementById('job-tracker-dialog-container');
    if (existingDialog) {
        existingDialog.remove();
    }

    // Inject styles first (with theme support)
    await injectStyle();

    const dialogContainer = document.createElement('div');
    dialogContainer.id = 'job-tracker-dialog-container';
    document.body.appendChild(dialogContainer);

    const root = createRoot(dialogContainer);
    root.render(<JobTrackerDialog />);
};

const injectStyle = async () => {
    // Load theme from storage
    let theme = 'light';
    try {
        const result = await chrome.storage.local.get(['theme']);
        theme = result.theme || 'light';
    } catch (error) {
        console.error('Error loading theme:', error);
    }

    const styleText = `
    /* Job Tracker Extension Styles - Isolated */
    #job-tracker-dialog-container {
        position: fixed;
        top: 100px;
        right: 100px;
        z-index: 999999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: ${theme === 'dark' ? '#f9fafb' : '#111827'};
    }

    /* Minimal universal reset - only the most critical properties */
    #job-tracker-dialog-container *,
    #job-tracker-dialog-container *::before,
    #job-tracker-dialog-container *::after {
        box-sizing: border-box !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    /* Reset only problematic inherited styles */
    #job-tracker-dialog-container {
        line-height: 1.5 !important;
        color: ${theme === 'dark' ? '#f9fafb' : '#111827'} !important;
        font-size: 14px !important;
        text-decoration: none !important;
        text-transform: none !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
        text-indent: 0 !important;
        text-shadow: none !important;
        text-align: left !important;
        white-space: normal !important;
        list-style: none !important;
    }
    /* Dialog container */
    #job-tracker-dialog-container .job-tracker-dialog {
        border-radius: 12px !important;
        background: ${theme === 'dark' ? '#1f2937' : 'white'} !important;
        border: 1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'} !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, ${theme === 'dark' ? '0.3' : '0.1'}), 0 10px 10px -5px rgba(0, 0, 0, ${theme === 'dark' ? '0.2' : '0.04'}) !important;
        width: 520px !important;
        max-width: 95vw !important;
        max-height: 85vh !important;
        overflow: hidden !important;
        animation: slideIn 0.3s ease-out !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        display: flex !important;
        flex-direction: column !important;
        position: relative !important;
        z-index: 999999 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.5 !important;
        font-size: 14px !important;
        color: ${theme === 'dark' ? '#f9fafb' : '#111827'} !important;
    }

    /* Selective resets to prevent host page interference while preserving dialog functionality */
    #job-tracker-dialog-container div:not(.job-tracker-dialog):not(.job-tracker-dialog-header):not(.job-tracker-dialog-content):not(.save-button-container):not(.remote-highlight):not(.info-grid):not(.content-section):not(.tech-stack-container):not(.job-description):not(.no-content):not(.loading-state):not(.loading-content) {
        line-height: 1.5 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        border: none !important;
        text-decoration: none !important;
        text-transform: none !important;
        text-shadow: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        float: none !important;
        clear: none !important;
    }

    /* Protect text elements from host page styles */
    #job-tracker-dialog-container span,
    #job-tracker-dialog-container p,
    #job-tracker-dialog-container h1,
    #job-tracker-dialog-container h2,
    #job-tracker-dialog-container h3,
    #job-tracker-dialog-container label {
        line-height: 1.5 !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        text-decoration: none !important;
        text-transform: none !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
        text-indent: 0 !important;
        text-shadow: none !important;
        font-weight: normal !important;
        font-style: normal !important;
        text-align: inherit !important;
    }

    /* Protect form elements */
    #job-tracker-dialog-container input,
    #job-tracker-dialog-container button {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        line-height: 1.5 !important;
        text-decoration: none !important;
        text-transform: none !important;
        letter-spacing: normal !important;
        appearance: none !important;
        outline: none !important;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* Dialog header */
    #job-tracker-dialog-container .job-tracker-dialog-header {
        background: linear-gradient(135deg, ${theme === 'dark' ? '#1e40af' : '#3b82f6'} 0%, ${theme === 'dark' ? '#1e3a8a' : '#2563eb'} 100%);
        color: white;
        padding: 16px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: move;
        user-select: none;
    }

    /* Drag handle */
    #job-tracker-dialog-container .job-tracker-handle {
        display: flex;
        align-items: center;
        flex: 1;
        font-weight: 600;
        font-size: 16px;
    }

    /* Close button */
    #job-tracker-dialog-container .job-tracker-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    #job-tracker-dialog-container .job-tracker-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }

    /* Dialog content */
    #job-tracker-dialog-container .job-tracker-dialog-content {
        padding: 16px 20px;
        flex: 1;
        overflow-y: auto;
    }

    /* Form field */
    #job-tracker-dialog-container .form-field {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin-bottom: 16px;
    }

    /* Label */
    #job-tracker-dialog-container .input-label {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* Icon styling */
    #job-tracker-dialog-container .input-label svg,
    #job-tracker-dialog-container .job-tracker-handle svg,
    #job-tracker-dialog-container .job-tracker-close svg,
    #job-tracker-dialog-container .btn svg,
    #job-tracker-dialog-container .save-button svg {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
    }

    #job-tracker-dialog-container .job-tracker-handle svg {
        width: 20px;
        height: 20px;
    }

    /* Input field */
    #job-tracker-dialog-container .input-field {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'};
        transition: all 0.2s ease;
        outline: none;
        font-size: 14px;
        background: ${theme === 'dark' ? '#374151' : 'white'};
        color: ${theme === 'dark' ? '#f9fafb' : '#111827'};
    }

    #job-tracker-dialog-container .input-field:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    #job-tracker-dialog-container .input-field::placeholder {
        color: #9ca3af;
    }

    /* Textarea */
    #job-tracker-dialog-container textarea.input-field {
        resize: vertical;
        min-height: 80px;
    }

    /* Select */
    #job-tracker-dialog-container select.input-field {
        cursor: pointer;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px;
    }

    /* Button Styles */
    #job-tracker-dialog-container .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
        border: none;
        text-decoration: none;
        font-size: 14px;
        line-height: 1.25;
        gap: 8px;
        padding: 12px 20px;
        outline: none;
    }

    #job-tracker-dialog-container .btn:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }

    #job-tracker-dialog-container .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
    }

    #job-tracker-dialog-container .btn-primary:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    #job-tracker-dialog-container .btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    #job-tracker-dialog-container .btn-success:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    #job-tracker-dialog-container .btn-outline {
        background: white;
        border: 1px solid #d1d5db;
        color: #374151;
    }

    #job-tracker-dialog-container .btn-outline:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }

    #job-tracker-dialog-container .btn-full {
        width: 100%;
    }

    #job-tracker-dialog-container .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
    }

    /* Save button container - sticky at bottom */
    #job-tracker-dialog-container .save-button-container {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        border-top: 1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'};
        padding: 16px 20px;
        background: linear-gradient(135deg, ${theme === 'dark' ? '#374151' : '#f9fafb'} 0%, ${theme === 'dark' ? '#4b5563' : '#f3f4f6'} 100%);
        backdrop-filter: blur(8px);
        z-index: 10;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, ${theme === 'dark' ? '0.3' : '0.1'});
    }

    /* Save button specific */
    #job-tracker-dialog-container .save-button {
        width: 100%;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 14px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }

    #job-tracker-dialog-container .save-button:hover:not(:disabled) {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    #job-tracker-dialog-container .save-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    /* Content sections */
    #job-tracker-dialog-container .content-section {
        margin-bottom: 18px;
    }

    #job-tracker-dialog-container .section-label {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    #job-tracker-dialog-container .section-label svg {
        width: 14px;
        height: 14px;
        flex-shrink: 0;
    }

    #job-tracker-dialog-container .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 16px;
    }

    #job-tracker-dialog-container .info-card {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px;
    }

    #job-tracker-dialog-container .info-card-label {
        font-size: 11px;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 4px;
    }

    #job-tracker-dialog-container .info-card-value {
        font-size: 14px;
        font-weight: 500;
        color: #111827;
    }

    /* Remote availability highlight */
    #job-tracker-dialog-container .remote-highlight {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        border: 1px solid #93c5fd;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    #job-tracker-dialog-container .remote-highlight svg {
        width: 20px;
        height: 20px;
        color: #2563eb;
        flex-shrink: 0;
    }

    #job-tracker-dialog-container .remote-highlight .remote-label {
        font-size: 11px;
        font-weight: 600;
        color: #1e40af;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
    }

    #job-tracker-dialog-container .remote-highlight .remote-value {
        font-size: 14px;
        font-weight: 600;
        color: #1e3a8a;
    }

    /* Tech stack chips */
    #job-tracker-dialog-container .tech-stack-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 8px;
    }

    #job-tracker-dialog-container .tech-chip {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
        border: 1px solid #93c5fd;
        transition: all 0.2s ease;
    }

    #job-tracker-dialog-container .tech-chip:hover {
        background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
        transform: translateY(-1px);
    }

    /* No content placeholder */
    #job-tracker-dialog-container .no-content {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        color: #6b7280;
        font-size: 14px;
    }

    /* Job description */
    #job-tracker-dialog-container .job-description {
        background: ${theme === 'dark' ? '#374151' : '#f9fafb'};
        border: 1px solid ${theme === 'dark' ? '#4b5563' : '#e5e7eb'};
        border-radius: 8px;
        padding: 12px;
        max-height: 150px;
        overflow-y: auto;
    }

    #job-tracker-dialog-container .job-description .prose {
        font-size: 14px;
        line-height: 1.6;
        color: #374151;
    }

    #job-tracker-dialog-container .job-description .prose h1,
    #job-tracker-dialog-container .job-description .prose h2,
    #job-tracker-dialog-container .job-description .prose h3 {
        font-weight: 600;
        margin-bottom: 8px;
        color: #111827;
    }

    #job-tracker-dialog-container .job-description .prose p {
        margin-bottom: 12px;
    }

    #job-tracker-dialog-container .job-description .prose ul,
    #job-tracker-dialog-container .job-description .prose ol {
        margin-left: 20px;
        margin-bottom: 12px;
    }

    #job-tracker-dialog-container .job-description .prose li {
        margin-bottom: 4px;
    }

    /* Account item */
    #job-tracker-dialog-container .account-item {
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.2s ease;
        cursor: pointer;
        background: white;
    }

    #job-tracker-dialog-container .account-item:hover {
        background-color: #f9fafb;
        border-color: #d1d5db;
    }

    #job-tracker-dialog-container .account-item.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }

    /* Toast notification */
    #job-tracker-dialog-container .toast {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        z-index: 999999;
        font-size: 14px;
        font-weight: 500;
    }

/* Animations */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

    /* Toast Notifications - Global styles with maximum specificity */
    body .job-tracker-toast,
    html .job-tracker-toast,
    div.job-tracker-toast {
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        z-index: 2147483647 !important; /* Maximum z-index value */
        min-width: 300px !important;
        max-width: 400px !important;
        padding: 16px !important;
        border-radius: 8px !important;
        color: white !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        font-weight: normal !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        transform: translateX(0) !important;
        transition: all 0.3s ease-in-out !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        margin: 0 !important;
        border: none !important;
        outline: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
        overflow: visible !important;
        clip: auto !important;
        filter: none !important;
        width: auto !important;
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        left: auto !important;
        bottom: auto !important;
        float: none !important;
        clear: none !important;
        vertical-align: baseline !important;
        text-decoration: none !important;
        text-transform: none !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
        text-indent: 0 !important;
        text-shadow: none !important;
        white-space: normal !important;
        word-wrap: normal !important;
        word-break: normal !important;
        hyphens: none !important;
        user-select: auto !important;
        cursor: auto !important;
        resize: none !important;
        appearance: none !important;
        box-sizing: border-box !important;
        line-height: 1.4 !important;
        text-align: left !important;
    }

    body .job-tracker-toast.show,
    html .job-tracker-toast.show,
    div.job-tracker-toast.show {
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    body .job-tracker-toast.info,
    html .job-tracker-toast.info,
    div.job-tracker-toast.info {
        background-color: #3b82f6 !important;
        background: #3b82f6 !important;
    }

    body .job-tracker-toast.success,
    html .job-tracker-toast.success,
    div.job-tracker-toast.success {
        background-color: #10b981 !important;
        background: #10b981 !important;
    }

    body .job-tracker-toast.error,
    html .job-tracker-toast.error,
    div.job-tracker-toast.error {
        background-color: #ef4444 !important;
        background: #ef4444 !important;
    }

    body .job-tracker-toast.loading,
    html .job-tracker-toast.loading,
    div.job-tracker-toast.loading {
        background-color: #6366f1 !important;
        background: #6366f1 !important;
    }

    .job-tracker-toast .job-tracker-toast-icon {
        flex-shrink: 0 !important;
        width: 16px !important;
        height: 16px !important;
        min-width: 16px !important;
        min-height: 16px !important;
        max-width: 16px !important;
        max-height: 16px !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: none !important;
        fill: currentColor !important;
        color: inherit !important;
    }

    .job-tracker-toast .job-tracker-toast-spinner {
        width: 16px !important;
        height: 16px !important;
        min-width: 16px !important;
        min-height: 16px !important;
        max-width: 16px !important;
        max-height: 16px !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-top: 2px solid white !important;
        border-radius: 50% !important;
        animation: job-tracker-spin 1s linear infinite !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        box-sizing: border-box !important;
        position: relative !important;
        z-index: 1 !important;
    }

    .job-tracker-toast .job-tracker-toast-content {
        flex: 1 !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .job-tracker-toast .job-tracker-toast-title {
        font-weight: 600 !important;
        margin-bottom: 4px !important;
        color: white !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
    }

    .job-tracker-toast .job-tracker-toast-message {
        opacity: 0.9 !important;
        font-size: 13px !important;
        color: white !important;
        display: block !important;
        visibility: visible !important;
        line-height: 1.4 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        #job-tracker-dialog-container .job-tracker-dialog {
            width: 95vw;
            max-width: none;
            margin: 10px;
        }

        #job-tracker-dialog-container .job-tracker-dialog-content {
            padding: 16px;
        }

        #job-tracker-dialog-container .job-tracker-dialog-header {
            padding: 12px 16px;
        }

        .job-tracker-toast {
            right: 10px !important;
            left: 10px !important;
            min-width: auto !important;
            max-width: none !important;
        }
    }

    /* Loading state - with high specificity to override resets */
    #job-tracker-dialog-container .loading-state {
        padding: 40px 20px !important;
        text-align: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        min-height: 200px !important;
        background: transparent !important;
    }

    #job-tracker-dialog-container .loading-content {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 16px !important;
        background: transparent !important;
    }

    #job-tracker-dialog-container .loading-content h3 {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #111827 !important;
        margin: 0 !important;
        padding: 0 !important;
        text-align: center !important;
        line-height: 1.4 !important;
        background: transparent !important;
        border: none !important;
    }

    #job-tracker-dialog-container .loading-content p {
        color: #6b7280 !important;
        font-size: 14px !important;
        margin: 0 !important;
        padding: 0 !important;
        text-align: center !important;
        line-height: 1.4 !important;
        background: transparent !important;
        border: none !important;
    }

    #job-tracker-dialog-container .loading-spinner {
        width: 40px !important;
        height: 40px !important;
        border: 3px solid #e5e7eb !important;
        border-top: 3px solid #3b82f6 !important;
        border-radius: 50% !important;
        animation: job-tracker-spin 1s linear infinite !important;
        margin: 0 auto !important;
        padding: 0 !important;
        background: transparent !important;
        display: block !important;
        flex-shrink: 0 !important;
        position: relative !important;
        z-index: 1 !important;
        box-sizing: border-box !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: none !important;
        filter: none !important;
        clip: auto !important;
        overflow: visible !important;
        min-width: 40px !important;
        min-height: 40px !important;
        max-width: 40px !important;
        max-height: 40px !important;
    }

    /* Extra specific rule to ensure spinner is always visible */
    #job-tracker-dialog-container .loading-state .loading-content .loading-spinner {
        width: 40px !important;
        height: 40px !important;
        border: 3px solid #e5e7eb !important;
        border-top: 3px solid #3b82f6 !important;
        border-radius: 50% !important;
        animation: job-tracker-spin 1s linear infinite !important;
        display: block !important;
        margin: 0 auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Success state */
    #job-tracker-dialog-container .success {
        border-color: #10b981;
        background-color: #ecfdf5;
    }

    /* Error state */
    #job-tracker-dialog-container .error {
        border-color: #ef4444;
        background-color: #fef2f2;
    }

    /* Spinner animation - ensure it's always visible */
    @keyframes job-tracker-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Additional specificity for spinner visibility */
    body .job-tracker-toast .job-tracker-toast-spinner,
    html .job-tracker-toast .job-tracker-toast-spinner,
    div.job-tracker-toast .job-tracker-toast-spinner {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        animation: job-tracker-spin 1s linear infinite !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-top: 2px solid white !important;
        border-radius: 50% !important;
        width: 16px !important;
        height: 16px !important;
        box-sizing: border-box !important;
    }
    `;
    const styles = document.createElement('style');
    styles.textContent = styleText;
    document.head.appendChild(styles);
}

const showToast = (message: string, type: 'info' | 'success' | 'error' | 'loading' = 'info', duration: number = 3000) => {
    // Remove existing toast if any
    const existingToast = document.getElementById('job-tracker-toast');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.id = 'job-tracker-toast';
    toast.className = `job-tracker-toast ${type}`;

    // Start with visible position to avoid hiding issues
    const baseStyles = `
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        left: auto !important;
        z-index: 2147483647 !important;
        width: 320px !important;
        max-width: calc(100vw - 40px) !important;
        min-width: 280px !important;
        padding: 16px !important;
        border-radius: 8px !important;
        color: white !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        font-weight: normal !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        transform: translateX(0) !important;
        opacity: 1 !important;
        transition: all 0.3s ease-in-out !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        margin: 0 !important;
        border: none !important;
        outline: none !important;
        visibility: visible !important;
        pointer-events: auto !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        line-height: 1.4 !important;
        text-align: left !important;
    `;

    let bgColor = '';
    switch (type) {
        case 'success': bgColor = 'background: #10b981 !important;'; break;
        case 'error': bgColor = 'background: #ef4444 !important;'; break;
        case 'loading': bgColor = 'background: #6366f1 !important;'; break;
        default: bgColor = 'background: #3b82f6 !important;'; break;
    }

    toast.style.cssText = baseStyles + bgColor;

    let icon = '';
    let title = '';

    switch (type) {
        case 'loading':
            icon = '<div class="job-tracker-toast-spinner"></div>';
            title = 'Processing...';
            break;
        case 'success':
            icon = '<svg class="job-tracker-toast-icon" width="16" height="16" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
            title = 'Success';
            break;
        case 'error':
            icon = '<svg class="job-tracker-toast-icon" width="16" height="16" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
            title = 'Error';
            break;
        default:
            icon = '<svg class="job-tracker-toast-icon" width="16" height="16" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
            title = 'Info';
    }

    toast.innerHTML = `
        ${icon}
        <div class="job-tracker-toast-content">
            <div class="job-tracker-toast-title">${title}</div>
            <div class="job-tracker-toast-message">${message}</div>
        </div>
    `;

    document.body.appendChild(toast);

    // Toast is already visible due to inline styles, no animation delay needed
    toast.classList.add('show');

    // Auto remove (except for loading type)
    if (type !== 'loading' && duration > 0) {
        setTimeout(() => {
            // Fade out animation
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(calc(100% + 20px))';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    return toast;
};

// Make showToast globally available
(window as any).showJobTrackerToast = showToast;

let currentHotkey = '';

const addHotkeyListener = async () => {
    // Load initial hotkey
    currentHotkey = await getHotkey() || 'Control+F11';

    document.addEventListener('keydown', (e) => {
        // Prevent triggering in extension popup or when focused on input elements
        if (window.location.protocol === 'chrome-extension:' ||
            e.target instanceof HTMLInputElement ||
            e.target instanceof HTMLTextAreaElement ||
            (e.target as HTMLElement)?.contentEditable === 'true') {
            return;
        }

        const keyCombo = `${e.ctrlKey ? 'Control+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.altKey ? 'Alt+' : ''}${e.key}`;

        if (keyCombo === currentHotkey) {
            e.preventDefault();
            injectDialog(); // injectDialog now calls injectStyle internally
        }
    });

    // Listen for hotkey updates from background script
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
        console.log('Global message listener received:', message);

        if (message.type === 'HOTKEY_UPDATED') {
            currentHotkey = message.hotkey;
            console.log('Hotkey updated to:', currentHotkey);
        }

        if (message.type === 'SHOW_TOAST') {
            showToast(message.message, message.toastType || 'info', message.duration);
        }

        if (message.type === 'SHOW_DIALOG') {
            console.log('Global listener: Showing dialog...');
            injectDialog();
            sendResponse({ success: true });
            return true; // Keep message channel open for async response
        }
    });
};

// Skip execution if running inside the Chrome extension popup or options page
if (window.location.protocol === 'chrome-extension:') {
    console.log('[Job Tracker] Skipping hotkey listener inside extension UI');
} else {
    addHotkeyListener(); // add listener in normal web pages
}

(window as any).contentScriptLoaded = true;

(window as any).runTracker = function () {
    injectDialog(); // injectDialog now calls injectStyle internally
};

console.log('Job Tracker Content Script Loaded');