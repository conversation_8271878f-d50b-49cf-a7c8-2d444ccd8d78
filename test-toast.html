<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .info { background-color: #3b82f6; color: white; }
        .success { background-color: #10b981; color: white; }
        .error { background-color: #ef4444; color: white; }
        .loading { background-color: #6366f1; color: white; }
        .clear { background-color: #6b7280; color: white; }
    </style>
</head>
<body>
    <h1>Toast Notification Test</h1>
    <p>Click the buttons below to test different toast types:</p>
    
    <div class="test-buttons">
        <button class="info" onclick="testToast('info')">Show Info Toast</button>
        <button class="success" onclick="testToast('success')">Show Success Toast</button>
        <button class="error" onclick="testToast('error')">Show Error Toast</button>
        <button class="loading" onclick="testToast('loading')">Show Loading Toast</button>
        <button class="clear" onclick="clearToast()">Clear Toast</button>
    </div>

    <div id="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Load this page in a browser where the job tracker extension is installed</li>
            <li>Open the browser console (F12)</li>
            <li>Click the buttons above to test different toast types</li>
            <li>Check if:
                <ul>
                    <li>Toasts appear in the top-right corner</li>
                    <li>Icons are properly sized (16px)</li>
                    <li>Loading spinner is visible and animating</li>
                    <li>Success checkmark is not too big</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        function testToast(type) {
            const messages = {
                info: 'This is an info message',
                success: 'Operation completed successfully!',
                error: 'Something went wrong!',
                loading: 'Processing your request...'
            };
            
            if (window.showJobTrackerToast) {
                window.showJobTrackerToast(messages[type], type);
                console.log(`Showing ${type} toast:`, messages[type]);
            } else {
                console.error('showJobTrackerToast function not found. Make sure the extension is loaded.');
                alert('Extension not loaded. Please make sure the job tracker extension is installed and active.');
            }
        }
        
        function clearToast() {
            const existingToast = document.getElementById('job-tracker-toast');
            if (existingToast) {
                existingToast.remove();
                console.log('Toast cleared');
            } else {
                console.log('No toast to clear');
            }
        }
        
        // Check if extension is loaded
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (window.showJobTrackerToast) {
                    console.log('✅ Job Tracker extension detected');
                } else {
                    console.warn('⚠️ Job Tracker extension not detected');
                }
            }, 1000);
        });
    </script>
</body>
</html>
